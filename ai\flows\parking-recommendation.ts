
// Parking Recommendation Flow
'use server';

/**
 * @fileOverview Provides smart parking recommendations based on user preferences and real-time conditions.
 *
 * - parkingRecommendation - A function that provides parking recommendations.
 * - ParkingRecommendationInput - The input type for the parkingRecommendation function.
 * - ParkingRecommendationOutput - The return type for the parkingRecommendation function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ParkingRecommendationInputSchema = z.object({
  userPreferences: z
    .string()
    .describe('用户停车偏好，例如价格敏感度、距离偏好、是否需要室内停车等。'),
  realTimeConditions: z
    .string()
    .describe('实时状况，例如当前交通情况、天气、停车场可用性等。'),
});

export type ParkingRecommendationInput = z.infer<typeof ParkingRecommendationInputSchema>;

const ParkingRecommendationOutputSchema = z.object({
  recommendation: z
    .string()
    .describe(
      '详细的最佳停车位置推荐，包括推荐理由、价格、距离以及其他相关细节。'
    ),
});

export type ParkingRecommendationOutput = z.infer<typeof ParkingRecommendationOutputSchema>;

export async function parkingRecommendation(input: ParkingRecommendationInput): Promise<ParkingRecommendationOutput> {
  return parkingRecommendationFlow(input);
}

// The core prompt instructions are kept in English for model consistency,
// as translating them might alter nuanced behavior. 
// The input field descriptions (above) are translated for UI/API clarity.
const prompt = ai.definePrompt({
  name: 'parkingRecommendationPrompt',
  input: {schema: ParkingRecommendationInputSchema},
  output: {schema: ParkingRecommendationOutputSchema},
  prompt: `You are an AI parking assistant providing smart parking recommendations based on user preferences and real-time conditions.

  Consider the following user preferences:
  {{userPreferences}}

  Also, consider the following real-time conditions:
  {{realTimeConditions}}

  Provide a detailed recommendation of the optimal parking location, including the reasons for the recommendation, price, distance, and any other relevant details.
  The recommendation should be concise and easy to understand. Be specific about why you are recommending this place.
  If user preferences or real-time conditions are in Chinese, please provide the recommendation in Chinese.
  `,
});

const parkingRecommendationFlow = ai.defineFlow(
  {
    name: 'parkingRecommendationFlow',
    inputSchema: ParkingRecommendationInputSchema,
    outputSchema: ParkingRecommendationOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);

