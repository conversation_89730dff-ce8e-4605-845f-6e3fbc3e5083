
"use client"; 

import { InteractiveMap } from "@/components/map/InteractiveMap";
import { ParkingAvailabilityCard } from "@/components/parking/ParkingAvailabilityCard";
import type { ParkingSpot } from "@/components/parking/ParkingAvailabilityCard";
import { SearchBar } from "@/components/search/SearchBar";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";

const detectionMethods = ["地磁传感", "超声波传感", "视频监控"];
const getRandomDetectionMethod = () => detectionMethods[Math.floor(Math.random() * detectionMethods.length)] as "地磁传感" | "超声波传感" | "视频监控";

// 坐标数据来源：高德地图坐标拾取器 (大致在北京市区范围内)
const initialParkingSpotsData: ParkingSpot[] = [
  { id: "ps1", name: "市中心地下车库", available: 50, total: 200, rate: "¥15/小时", hours: "24小时", type: "garage", restrictions: "限高2.5米，大型SUV需注意。", distance: "步行5分钟", detectionMethod: "超声波传感", lat: 39.90923, lng: 116.397428 }, // 天安门广场附近
  { id: "ps2", name: "城市广场露天停车场", available: 15, total: 50, rate: "¥10/小时", hours: "早6点 - 晚10点", type: "lot", distance: "步行2分钟", detectionMethod: "超声波传感", lat: 39.90223, lng: 116.407428 }, // 王府井附近
  { id: "ps3", name: "北方购物中心停车场", available: 120, total: 300, rate: "¥12/小时", hours: "早8点 - 午夜12点", type: "garage", distance: "步行10分钟", detectionMethod: "超声波传感", lat: 39.917999, lng: 116.390006 }, // 西单附近
  { id: "ps4", name: "主干道路边停车位", available: 0, total: 20, rate: "¥5/小时 (最多2小时)", hours: "周一至周六 早9点 - 晚6点", type: "street", restrictions: "晚6点后为G区居民专用。", distance: "路边", detectionMethod: "地磁传感", lat: 39.925996, lng: 116.403781 }, // 东单附近
  { id: "ps5", name: "科技园区综合停车场", available: 220, total: 500, rate: "¥8/小时, ¥50封顶", hours: "工作日 早7点 - 晚7点", type: "garage", restrictions: "提供电动汽车充电桩。", distance: "步行12分钟", detectionMethod: "超声波传感", lat: 39.982903, lng: 116.312654 }, // 中关村附近
  { id: "ps6", name: "医院附属停车场", available: 5, total: 80, rate: "¥10/小时", hours: "24小时", type: "garage", restrictions: "夜间入口变更。", distance: "步行1分钟", detectionMethod: "视频监控", lat: 39.933501, lng: 116.371225 }, // 积水潭医院附近
  { id: "ps7", name: "体育中心露天场地", available: 250, total: 400, rate: "¥5/小时, 大型活动¥20/次", hours: "活动日开放", type: "lot", distance: "步行8分钟", detectionMethod: "地磁传感", lat: 39.966953, lng: 116.40183 }, // 国家奥林匹克体育中心附近
  { id: "demo1", name: "演示点1停车场", available: 10, total: 20, rate: "¥8/小时", hours: "08:00-22:00", type: "lot", restrictions: "仅限小型车", distance: "步行3分钟", detectionMethod: "地磁传感", lat: 39.915, lng: 116.404 }, 
  { id: "demo2", name: "演示点2车库", available: 5, total: 15, rate: "¥12/小时", hours: "24小时", type: "garage", restrictions: "有充电桩", distance: "步行1分钟", detectionMethod: "视频监控", lat: 39.900, lng: 116.390 },
];


export default function DashboardPage() {
  const [parkingSpots, setParkingSpots] = useState<ParkingSpot[]>([]);
  const [displayedSpots, setDisplayedSpots] = useState<ParkingSpot[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    // Client-side only data processing to avoid hydration errors with Math.random (if still used)
    // and to ensure dynamic data like detectionMethod is set.
    const clientSideParkingData = initialParkingSpotsData.map(spot => ({
      ...spot,
      // Ensure detectionMethod has a value, if not already provided in initialParkingSpotsData
      detectionMethod: spot.detectionMethod || getRandomDetectionMethod(), 
    }));
    setParkingSpots(clientSideParkingData);
    setDisplayedSpots(clientSideParkingData);
  }, []);


  const handleSearch = (searchTerm: string, filterType: string) => {
    const term = searchTerm.toLowerCase();
    let filteredSpots = parkingSpots;

    if (term) {
      filteredSpots = filteredSpots.filter(spot => 
        spot.name.toLowerCase().includes(term) ||
        (spot.restrictions && spot.restrictions.toLowerCase().includes(term)) ||
        spot.rate.toLowerCase().includes(term) 
      );
    }

    if (filterType !== "all") {
      if (filterType === "covered") {
        filteredSpots = filteredSpots.filter(spot => spot.type === 'garage');
      } else if (filterType === "ev_charging") {
        filteredSpots = filteredSpots.filter(spot => spot.restrictions && spot.restrictions.toLowerCase().includes("电动汽车充电桩"));
      } else if (filterType === "garage" || filterType === "street" || filterType === "lot") {
        filteredSpots = filteredSpots.filter(spot => spot.type === filterType);
      }
    }
    
    setDisplayedSpots(filteredSpots);

    if (filteredSpots.length === 0) {
      toast({
        title: "无匹配结果",
        description: "未找到符合您筛选条件的停车位。请尝试调整搜索条件。",
        variant: "default", // Changed from destructive for a softer notification
        duration: 4000,
      });
    } else {
       toast({
        title: "搜索完成",
        description: `找到 ${filteredSpots.length} 个匹配的停车位。`,
        variant: "default",
        duration: 3000,
      });
    }
  };
  
  if (parkingSpots.length === 0) { // Check if parkingSpots is empty (during initial client-side processing)
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <p className="text-muted-foreground">正在加载停车位数据...</p>
      </div>
    );
  }


  return (
    <div className="flex flex-col xl:flex-row gap-6 h-full min-h-[600px]">
      <div className="xl:w-3/5 flex flex-col gap-6 min-h-0">
        <SearchBar onSearch={handleSearch} />
        <div className="flex-grow min-h-[400px]">
          <InteractiveMap parkingSpots={displayedSpots} />
        </div>
      </div>
      <Separator orientation="vertical" className="hidden xl:block mx-0" />
      <div className="xl:w-2/5 flex flex-col overflow-hidden">
        <h2 className="text-xl font-semibold mb-4 text-foreground px-1 shrink-0">
          实时车位信息 {displayedSpots.length !== parkingSpots.length && `(${displayedSpots.length}个结果)`}
        </h2>
        {displayedSpots.length > 0 ? (
          <ScrollArea className="flex-grow pr-2">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-1 gap-4 pb-4">
              {displayedSpots.map((spot) => (
                <ParkingAvailabilityCard key={spot.id} spot={spot} />
              ))}
            </div>
            <ScrollBar orientation="vertical" />
          </ScrollArea>
        ) : (
          <div className="flex-grow flex items-center justify-center">
            <p className="text-muted-foreground text-center p-4">
              没有找到符合当前条件的停车位。
              <br />
              请尝试更改搜索词或筛选条件。
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

