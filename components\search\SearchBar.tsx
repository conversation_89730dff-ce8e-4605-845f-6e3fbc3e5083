
"use client";

import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel } from "@/components/ui/select";
import { SearchIcon, FilterIcon, DollarSignIcon, ClockIcon, ZapIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type React from "react";
import { useState } from "react";

interface SearchBarProps {
  onSearch: (searchTerm: string, filterType: string) => void;
}

export function SearchBar({ onSearch }: SearchBarProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");

  const handleSearchSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    onSearch(searchTerm, filterType);
  };

  const handleQuickFilterClick = (type: string) => {
    // For quick filters, we can use the current search term and apply a specific filter type
    // Or, they can have their own predefined search terms if needed.
    // For now, let's assume they use the current searchTerm and a specific type.
    // The actual complex logic (like sorting by price) is handled by onSearch if implemented there.
    
    // Let's update the filterType state and then call onSearch
    // setFilterType(type); // This might be too slow if onSearch relies on it directly
    // onSearch(searchTerm, type);

    // For "最优价格", "最近距离", "最快停车", true filtering/sorting is complex with current static data.
    // The toast message will clarify these are advanced features.
    let description = `将尝试根据 "${searchTerm || '任何地点'}" 和 "${filterType}" 类型进行筛选。`;
    if (type === 'price') {
      description = "“最优价格”将尝试优先显示价格较低或有优惠的选项，当前基于文本和类型筛选。";
    } else if (type === 'distance') {
      description = "“最近距离”将尝试优先显示距离较近的选项，当前基于文本和类型筛选。";
    } else if (type === 'availability') { // Changed 'fastest' to 'availability' as it's more actionable
      description = "“空位优先”将尝试优先显示空余车位较多的选项，当前基于文本和类型筛选。";
    }
    
    toast({
      title: "高级筛选提示",
      description: description,
      duration: 5000,
    });
    // Trigger the main search with current text and selected dropdown,
    // actual advanced logic for price/distance/availability would be in page.tsx's onSearch
    onSearch(searchTerm, filterType); 
  };

  return (
    <form onSubmit={handleSearchSubmit} className="mb-6 p-4 bg-card rounded-lg shadow-md border">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
        <div className="lg:col-span-2">
          <label htmlFor="search-location" className="block text-sm font-medium text-muted-foreground mb-1">搜索位置或关键词</label>
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              id="search-location"
              name="search-location"
              type="text"
              placeholder="街道、地标、停车场名称或特点"
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="filter-type-trigger" className="block text-sm font-medium text-muted-foreground mb-1">停车类型</label>
          <Select name="filter-type" value={filterType} onValueChange={setFilterType}>
            <SelectTrigger id="filter-type-trigger">
              <FilterIcon className="mr-2 h-4 w-4 opacity-70" />
              <SelectValue placeholder="所有类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>常见类型</SelectLabel>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="garage">室内车库</SelectItem>
                <SelectItem value="street">路边停车</SelectItem>
                <SelectItem value="lot">露天停车场</SelectItem>
                <SelectItem value="covered">有遮挡 (同室内)</SelectItem>
              </SelectGroup>
              <SelectGroup>
                <SelectLabel>设施特点</SelectLabel>
                <SelectItem value="ev_charging">电动车充电</SelectItem>
                <SelectItem value="accessible" disabled>无障碍车位 (暂不可用)</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <Button type="submit" className="w-full h-10">
          <SearchIcon className="mr-2 h-4 w-4" />
          查找车位
        </Button>
      </div>
      <div className="mt-4 flex flex-wrap gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            type="button" 
            onClick={() => handleQuickFilterClick('price')}
          >
            <DollarSignIcon className="mr-1.5 h-3.5 w-3.5"/>最优价格
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            type="button"
            onClick={() => handleQuickFilterClick('distance')}
          >
            <ClockIcon className="mr-1.5 h-3.5 w-3.5"/>最近距离
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            type="button"
            onClick={() => handleQuickFilterClick('availability')}
          >
            <ZapIcon className="mr-1.5 h-3.5 w-3.5"/>空位优先
          </Button>
      </div>
    </form>
  );
}
