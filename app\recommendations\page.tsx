
"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { parkingRecommendation, ParkingRecommendationInput, ParkingRecommendationOutput } from "@/ai/flows/parking-recommendation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Loader2Icon, Wand2Icon, InfoIcon } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const recommendationFormSchema = z.object({
  userPreferences: z.string().min(10, "请更详细地描述您的偏好 (至少10个字符)。").max(500, "偏好描述不应超过500个字符。"),
  realTimeConditions: z.string().min(10, "请描述当前状况 (至少10个字符)。").max(500, "状况描述不应超过500个字符。"),
});

type RecommendationFormValues = z.infer<typeof recommendationFormSchema>;

export default function RecommendationsPage() {
  const [recommendation, setRecommendation] = useState<ParkingRecommendationOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialRealTimeConditions, setInitialRealTimeConditions] = useState<string>("");

  const form = useForm<RecommendationFormValues>({
    resolver: zodResolver(recommendationFormSchema),
    defaultValues: {
      userPreferences: "",
      realTimeConditions: "", 
    },
  });
  
  useEffect(() => {
    const date = new Date();
    const dynamicConditions = `截至 ${date.toLocaleTimeString()}, 总体交通状况一般，天气晴朗。市中心区域可能有活动导致的拥堵。`;
    setInitialRealTimeConditions(dynamicConditions);
    form.setValue("realTimeConditions", dynamicConditions);
  }, [form]);


  async function onSubmit(values: RecommendationFormValues) {
    setIsLoading(true);
    setError(null);
    setRecommendation(null);
    try {
      const result = await parkingRecommendation(values);
      setRecommendation(result);
    } catch (e) {
      console.error(e);
      setError(e instanceof Error ? e.message : "获取推荐时发生未知错误。");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="container mx-auto py-4 md:py-8">
      <Card className="max-w-2xl mx-auto shadow-xl border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Wand2Icon className="h-7 w-7 text-primary" />
            AI智能停车推荐
          </CardTitle>
          <CardDescription>
            让我们的AI根据您的偏好和当前状况，为您找到最佳停车位。
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="userPreferences"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">您的停车偏好</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="例如：我需要为SUV找一个室内停车位，靠近主街剧院。价格是次要考虑因素。大约晚上7点到达。"
                        {...field}
                        rows={4}
                        className="resize-none"
                      />
                    </FormControl>
                    <FormDescription>
                      您提供的信息越详细，推荐效果越好。
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="realTimeConditions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">当前状况 (自动填写，可编辑)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="例如：大雨，市政厅附近正在举办节日活动，导致道路封闭。"
                        {...field}
                        rows={3}
                        className="resize-none"
                      />
                    </FormControl>
                    <FormDescription>
                      如果您有更具体的实时信息，请调整。
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={isLoading} className="w-full text-lg py-6">
                {isLoading ? (
                  <>
                    <Loader2Icon className="mr-2 h-5 w-5 animate-spin" />
                    正在为您寻找车位...
                  </>
                ) : (
                  "获取AI推荐"
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        {recommendation && (
          <CardFooter className="flex-col items-start gap-4 pt-6 border-t">
             <h3 className="text-xl font-semibold text-primary">AI推荐结果:</h3>
            <Alert variant="default" className="bg-accent/10 border-accent/30 shadow-sm">
              <Wand2Icon className="h-5 w-5 text-accent" />
              <AlertTitle className="text-accent font-semibold">已找到最佳车位!</AlertTitle>
              <AlertDescription className="text-foreground/90 whitespace-pre-line">
                {recommendation.recommendation}
              </AlertDescription>
            </Alert>
          </CardFooter>
        )}
        {error && (
          <CardFooter className="pt-6 border-t">
            <Alert variant="destructive">
              <InfoIcon className="h-5 w-5" />
              <AlertTitle>推荐出错</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
