
"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { UserIcon, SaveIcon, MapPinIcon, CarIcon, ShieldCheckIcon, CoinsIcon } from "lucide-react";
import { useEffect } from "react";

const profileFormSchema = z.object({
  name: z.string().min(2, "姓名至少需要2个字符。").max(50, "姓名不能超过50个字符。"),
  email: z.string().email("无效的邮箱地址。"),
  preferredLocation: z.string().max(100, "地点名称过长").optional().or(z.literal('')),
  vehicleType: z.enum(["compact", "sedan", "suv", "truck", "motorcycle", "van", "other"]).optional(),
  prefersCoveredParking: z.boolean().default(false).optional(),
  prefersCheapParking: z.boolean().default(false).optional(),
  prefersSecureParking: z.boolean().default(false).optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// Dummy data simulation - in a real app, this would be fetched
const fetchUserProfile = async (): Promise<Partial<ProfileFormValues>> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return {
    name: "亚历克斯·帕克",
    email: "<EMAIL>",
    preferredLocation: "市中心金融区",
    vehicleType: "suv",
    prefersCoveredParking: true,
    prefersCheapParking: false,
    prefersSecureParking: true,
  };
};


export function ProfileForm() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
        name: "",
        email: "",
        preferredLocation: "",
        vehicleType: undefined,
        prefersCoveredParking: false,
        prefersCheapParking: false,
        prefersSecureParking: false,
    },
    mode: "onChange",
  });

  useEffect(() => {
    async function loadProfile() {
      try {
        const userProfile = await fetchUserProfile();
        form.reset(userProfile);
      } catch (error) {
        console.error("加载用户资料失败:", error);
        toast({
          title: "错误",
          description: "无法加载您的个人资料信息。",
          variant: "destructive",
        });
      }
    }
    loadProfile();
  }, [form]);


  function onSubmit(data: ProfileFormValues) {
    console.log("个人资料已提交:", data);
    toast({
      title: "资料已更新",
      description: "您的偏好设置已成功保存。",
      variant: "default",
    });
  }

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-xl border">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <UserIcon className="h-7 w-7 text-primary" />
          个人资料与偏好设置
        </CardTitle>
        <CardDescription>
          管理您的停车偏好和个人信息，以获得量身定制的推荐。
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-0">
          <CardContent className="space-y-6 pt-6">
            <fieldset className="space-y-6 p-4 border rounded-md">
              <legend className="text-lg font-medium text-primary px-1 -ml-1">基本信息</legend>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>姓名</FormLabel>
                      <FormControl>
                        <Input placeholder="您的姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>电子邮箱</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </fieldset>

            <fieldset className="space-y-6 p-4 border rounded-md">
              <legend className="text-lg font-medium text-primary px-1 -ml-1">停车偏好</legend>
              <FormField
                control={form.control}
                name="preferredLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>常用停车区域/地标</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                        <Input placeholder="例如：靠近市政厅，春田购物中心" className="pl-10" {...field} />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="vehicleType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>默认车辆类型</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                           <CarIcon className="mr-2 h-4 w-4 opacity-70" />
                          <SelectValue placeholder="选择您的车辆类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="compact">紧凑型轿车</SelectItem>
                        <SelectItem value="sedan">轿车</SelectItem>
                        <SelectItem value="suv">SUV</SelectItem>
                        <SelectItem value="van">面包车/小型货车</SelectItem>
                        <SelectItem value="truck">卡车</SelectItem>
                        <SelectItem value="motorcycle">摩托车</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-3">
                <FormField
                  control={form.control}
                  name="prefersCoveredParking"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3 bg-background hover:bg-muted/50 transition-colors">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} id="coveredParking" />
                      </FormControl>
                      <FormLabel htmlFor="coveredParking" className="font-normal cursor-pointer flex-grow">
                        偏好室内停车场
                      </FormLabel>
                    </FormItem>
                  )}
                />
                 <FormField
                  control={form.control}
                  name="prefersSecureParking"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3 bg-background hover:bg-muted/50 transition-colors">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} id="secureParking" />
                      </FormControl>
                       <FormLabel htmlFor="secureParking" className="font-normal cursor-pointer flex-grow">
                        偏好安全停车场 (如：监控、有管理员)
                      </FormLabel>
                      <ShieldCheckIcon className="h-5 w-5 text-accent ml-auto" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="prefersCheapParking"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3 bg-background hover:bg-muted/50 transition-colors">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} id="cheapParking" />
                      </FormControl>
                      <FormLabel htmlFor="cheapParking" className="font-normal cursor-pointer flex-grow">
                        优先选择经济型车位
                      </FormLabel>
                      <CoinsIcon className="h-5 w-5 text-yellow-500 ml-auto" />
                    </FormItem>
                  )}
                />
              </div>
            </fieldset>
          </CardContent>
          <CardFooter className="border-t px-6 py-4 mt-6">
            <Button type="submit" className="ml-auto text-base py-3 px-6" disabled={form.formState.isSubmitting}>
              <SaveIcon className="mr-2 h-5 w-5" />
              保存偏好
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
