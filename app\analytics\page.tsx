
"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Line, <PERSON><PERSON>hart, Pie, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart"; // ShadCN chart components
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState, useEffect } from 'react';

// Mock data
const generateHourlyDemandData = () => {
  const data = [];
  for (let i = 0; i < 24; i++) {
    data.push({
      hour: `${i.toString().padStart(2, '0')}:00`,
      demand: Math.floor(Math.random() * 80) + 20, // Random demand between 20 and 100
      predicted: Math.floor(Math.random() * 70) + 25, // Random prediction
    });
  }
  return data;
};

const popularTimesData = [
  { day: "周一", peakHours: Math.floor(Math.random() * 300) + 100 },
  { day: "周二", peakHours: Math.floor(Math.random() * 300) + 100 },
  { day: "周三", peakHours: Math.floor(Math.random() * 300) + 100 },
  { day: "周四", peakHours: Math.floor(Math.random() * 300) + 100 },
  { day: "周五", peakHours: Math.floor(Math.random() * 400) + 150 }, // Higher on Friday
  { day: "周六", peakHours: Math.floor(Math.random() * 500) + 200 }, // Highest on Saturday
  { day: "周日", peakHours: Math.floor(Math.random() * 450) + 180 },
];

const parkingTypeData = [
  { name: "室内车库", value: Math.floor(Math.random() * 400) + 200 },
  { name: "露天停车场", value: Math.floor(Math.random() * 300) + 100 },
  { name: "路边停车", value: Math.floor(Math.random() * 100) + 50 },
];

const PIE_COLORS = ["hsl(var(--chart-1))", "hsl(var(--chart-2))", "hsl(var(--chart-3))", "hsl(var(--chart-4))", "hsl(var(--chart-5))"];

export default function AnalyticsPage() {
  const [hourlyDemand, setHourlyDemand] = useState<any[]>([]);
  const [timeRange, setTimeRange] = useState<string>("next24h"); // 'next24h', 'last7days'

  useEffect(() => {
    // Prevent hydration errors by generating data on client
    setHourlyDemand(generateHourlyDemandData());
  }, []);


  const demandChartConfig = {
    demand: { label: "实际需求", color: "hsl(var(--chart-1))" },
    predicted: { label: "预测需求", color: "hsl(var(--chart-2))" },
  } satisfies Parameters<typeof ChartContainer>[0]["config"];

  const popularTimesChartConfig = {
    peakHours: { label: "高峰时段车辆数", color: "hsl(var(--chart-1))" },
  } satisfies Parameters<typeof ChartContainer>[0]["config"];
  
  const parkingTypeChartConfig = {
    value: { label: "车辆数" },
    室内车库: { color: PIE_COLORS[0] },
    露天停车场: { color: PIE_COLORS[1] },
    路边停车: { color: PIE_COLORS[2] },
  } satisfies Parameters<typeof ChartContainer>[0]["config"];


  if (hourlyDemand.length === 0) {
    // Still loading data or placeholder before client-side generation
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6 text-center">数据分析与预测</h1>
        <p className="text-center text-muted-foreground">正在加载分析数据...</p>
      </div>
    );
  }


  return (
    <div className="container mx-auto py-4 md:py-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-center text-primary">数据分析与预测</h1>
        <p className="text-muted-foreground text-center mt-2">
          基于历史和实时数据，洞察停车趋势，优化资源配置。
        </p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>车位需求趋势</CardTitle>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="next24h">未来24小时</SelectItem>
                  <SelectItem value="last7days" disabled>过去7天 (模拟)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <CardDescription>未来24小时车位需求预测与实际（模拟）对比。</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={demandChartConfig} className="h-[300px] w-full">
              <LineChart data={hourlyDemand} margin={{ left: 12, right: 12, top: 5, bottom: 5 }}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis dataKey="hour" tickLine={false} axisLine={false} tickMargin={8} />
                <YAxis tickLine={false} axisLine={false} tickMargin={8} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <ChartLegend content={<ChartLegendContent />} />
                <Line type="monotone" dataKey="demand" stroke="var(--color-demand)" strokeWidth={2} dot={false} />
                <Line type="monotone" dataKey="predicted" stroke="var(--color-predicted)" strokeWidth={2} strokeDasharray="5 5" dot={false} />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>热门停车时段</CardTitle>
            <CardDescription>一周内各天高峰时段停车车辆数统计。</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={popularTimesChartConfig} className="h-[300px] w-full">
              <BarChart data={popularTimesData} margin={{ left: 12, right: 12, top: 5, bottom: 5 }}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis dataKey="day" tickLine={false} axisLine={false} tickMargin={8} />
                <YAxis tickLine={false} axisLine={false} tickMargin={8} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <ChartLegend content={<ChartLegendContent />} />
                <Bar dataKey="peakHours" fill="var(--color-peakHours)" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card className="shadow-lg lg:col-span-2">
          <CardHeader>
            <CardTitle>停车类型分布</CardTitle>
            <CardDescription>各类停车场使用占比分析。</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <ChartContainer config={parkingTypeChartConfig} className="h-[300px] w-full max-w-md">
              <PieChart>
                <ChartTooltip content={<ChartTooltipContent hideLabel />} />
                <Pie data={parkingTypeData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={100} label>
                  {parkingTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                  ))}
                </Pie>
                <ChartLegend content={<ChartLegendContent />} />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
