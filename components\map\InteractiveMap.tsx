
"use client";

import React, { useEffect, useRef, useState } from 'react';
// AMapLoader will be imported dynamically
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { LocateFixedIcon, XIcon } from "lucide-react";
import type { ParkingSpot } from "@/components/parking/ParkingAvailabilityCard";
import { useToast } from "@/hooks/use-toast";


interface InteractiveMapProps {
  parkingSpots: ParkingSpot[];
}

interface MapSpot extends ParkingSpot {
  lat: number;
  lng: number;
  id: string;
}

const defaultParkingSpots: MapSpot[] = [
  { id: "s1", name: "演示点1", lat: 39.90923, lng: 116.397428, available: 10, total: 20, rate: "¥10/小时", hours: "24小时", type: "garage", detectionMethod: "地磁传感" },
  { id: "s2", name: "演示点2", lat: 39.90223, lng: 116.407428, available: 5, total: 10, rate: "¥12/小时", hours: "8-22点", type: "lot", detectionMethod: "超声波传感"},
];

export function InteractiveMap({ parkingSpots: propParkingSpots }: InteractiveMapProps) {
  const mapRef = useRef<any>(null);
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mapInstance, setMapInstance] = useState<any>(null);
  const [loadedAMapApi, setLoadedAMapApi] = useState<any>(null);
  const [clientProcessedSpots, setClientProcessedSpots] = useState<MapSpot[]>([]);
  const [userLocationMarker, setUserLocationMarker] = useState<any>(null);
  const { toast } = useToast();

  useEffect(() => {
    const spotsToProcess = propParkingSpots.length > 0 ? propParkingSpots : defaultParkingSpots;
    const processed = spotsToProcess.map((spot) => {
      const s = spot as any;
      return {
        ...spot,
        id: spot.id || `spot-${Math.random().toString(36).substr(2, 9)}`,
        lat: s.lat ?? (39.90923 + Math.random() * 0.05 - 0.025),
        lng: s.lng ?? (116.397428 + Math.random() * 0.05 - 0.025),
      } as MapSpot;
    });
    setClientProcessedSpots(processed);
  }, [propParkingSpots]);

  useEffect(() => {
    console.log("地图组件初始化开始");
    if (typeof window !== "undefined") {
      (window as any)._AMapSecurityConfig = {
        securityJsCode: 'f3c80377f70fd55fb72cbe1029a4e554',
      };

      console.log("开始加载高德地图API");
      import('@amap/amap-jsapi-loader')
        .then(({ default: AMapLoader }) => {
          console.log("AMapLoader导入成功");
          AMapLoader.load({
            key: '08b5c396ebcccf1ac1909cd635b2dd7c',
            version: '2.0',
            plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.Marker', 'AMap.InfoWindow', 'AMap.Geolocation'], // Added AMap.Geolocation
          })
          .then((AMap) => {
            console.log("高德地图API加载成功", AMap);
            setLoadedAMapApi(AMap);
            if (mapContainerRef.current && !mapRef.current) {
              console.log("创建地图实例");
              const map = new AMap.Map(mapContainerRef.current, {
                zoom: 11,
                center: [116.397428, 39.90923],
                viewMode: '2D',
              });
              map.addControl(new AMap.Scale());
              map.addControl(new AMap.ToolBar({ position: 'LT' })); // Changed to LT for better placement with new button

              setMapInstance(map);
              mapRef.current = map;
              setIsLoading(false);
              console.log("地图创建完成");
            } else if (mapRef.current) {
              setIsLoading(false);
              console.log("地图已存在，停止加载");
            }
          })
          .catch((e) => {
            console.error("高德地图JSAPI加载失败:", e);
            setIsLoading(false);
            toast({ title: "地图错误", description: "高德地图JSAPI加载失败。", variant: "destructive" });
          });
        })
        .catch((e) => {
          console.error("动态导入AMapLoader失败:", e);
          setIsLoading(false);
          toast({ title: "地图错误", description: "地图组件加载失败。", variant: "destructive" });
        });
    }

    return () => {
      if (mapRef.current) {
        mapRef.current.destroy();
        mapRef.current = null;
        setMapInstance(null);
        setLoadedAMapApi(null);
        if (userLocationMarker) {
          userLocationMarker.setMap(null);
          setUserLocationMarker(null);
        }
      }
    };
  }, [toast]); // Added toast to dependency array

  useEffect(() => {
    if (mapInstance && loadedAMapApi && clientProcessedSpots.length > 0) {
      mapInstance.clearMap(); 
      if (userLocationMarker) { // Re-add user marker if it exists
        userLocationMarker.setMap(mapInstance);
      }

      clientProcessedSpots.forEach(spot => {
        const marker = new loadedAMapApi.Marker({
          position: new loadedAMapApi.LngLat(spot.lng, spot.lat),
          title: spot.name,
        });

        const infoWindow = new loadedAMapApi.InfoWindow({
          content: `
            <div style="padding:10px; font-size:14px; color: #333;">
              <h4 style="margin:0 0 5px 0; font-size:16px;">${spot.name}</h4>
              <p>空余车位: ${spot.available}/${spot.total}</p>
              <p>费率: ${spot.rate}</p>
              <p>类型: ${spot.type === 'garage' ? '室内车库' : spot.type === 'lot' ? '露天停车场' : '路边'}</p>
              ${spot.detectionMethod ? `<p>检测方式: ${spot.detectionMethod}</p>` : ''}
            </div>
          `,
          offset: new loadedAMapApi.Pixel(0, -30)
        });

        marker.on('click', () => {
          infoWindow.open(mapInstance, marker.getPosition());
        });
        mapInstance.add(marker);
      });
      
      if (clientProcessedSpots.length > 0 && !userLocationMarker) { // Only fit view if not actively showing user location
         mapInstance.setFitView(); 
      }
    }
  }, [mapInstance, clientProcessedSpots, loadedAMapApi, userLocationMarker]);

  const handleGeolocate = () => {
    if (!mapInstance || !loadedAMapApi) {
      toast({ title: "地图未就绪", description: "请稍候再试。", variant: "default" });
      return;
    }

    if (userLocationMarker) { // If user marker exists, remove it and reset view
        userLocationMarker.setMap(null);
        setUserLocationMarker(null);
        if (clientProcessedSpots.length > 0) {
          mapInstance.setFitView();
        } else {
          mapInstance.setZoomAndCenter(11, [116.397428, 39.90923]);
        }
        toast({ title: "定位已清除", description: "已清除您的当前位置标记。", variant: "default" });
        return;
    }


    const geolocation = new loadedAMapApi.Geolocation({
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0,
      convert: true,
      showButton: false, // We use our own button
      showMarker: false, // We will create our own marker
      showCircle: true,
      circleOptions: {
        strokeColor: '#0093FF',
        strokeOpacity: 0.6,
        strokeWeight: 2,
        fillColor: '#0093FF',
        fillOpacity: 0.15
      }
    });

    mapInstance.add(geolocation); // Required to be added to map for events

    toast({ title: "正在定位...", description: "请允许浏览器获取您的位置信息。", variant: "default" });

    geolocation.getCurrentPosition((status: string, result: any) => {
      if (status === 'complete' && result.info === 'SUCCESS') {
        const position = result.position;
        
        if (userLocationMarker) {
          userLocationMarker.setMap(null);
        }

        const newUserMarker = new loadedAMapApi.Marker({
          position: position,
          icon: new loadedAMapApi.Icon({
            size: new loadedAMapApi.Size(25, 34),
            image: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
            imageSize: new loadedAMapApi.Size(25, 34),
          }),
          title: '我的位置',
          offset: new loadedAMapApi.Pixel(-12, -34)
        });
        newUserMarker.setMap(mapInstance);
        setUserLocationMarker(newUserMarker);

        mapInstance.setZoomAndCenter(15, position);
        toast({ title: "定位成功", description: `已在地图上标记您的位置。`, variant: "default" });

      } else {
        console.error('Geolocation error:', result);
        toast({ title: "定位失败", description: result.message || "无法获取您的当前位置，请检查定位权限或网络连接。", variant: "destructive" });
      }
      // Clean up: remove the geolocation control from the map if it adds UI elements or for safety,
      // though in this setup it's mostly for events.
      // mapInstance.remove(geolocation); // Could be problematic if events are still needed.
    });
  };

  return (
    <Card className="shadow-lg min-h-[400px] h-[400px] md:h-[500px] lg:h-[600px] flex flex-col overflow-hidden relative">
      <CardHeader className="pb-2 flex flex-row justify-between items-center">
        <div>
            <CardTitle className="text-xl">停车场位置概览</CardTitle>
            <CardDescription>点击地图上的标记查看详情。 {userLocationMarker && '点击定位按钮可清除当前位置。'}</CardDescription>
        </div>
        <Button
            variant="outline"
            size="icon"
            onClick={handleGeolocate}
            className="absolute top-4 right-4 z-10 bg-background/80 hover:bg-background"
            title={userLocationMarker ? "清除我的位置" : "定位我的位置"}
        >
            {userLocationMarker ? <XIcon className="h-5 w-5 text-destructive" /> : <LocateFixedIcon className="h-5 w-5 text-primary" /> }
        </Button>
      </CardHeader>
      <CardContent className="flex-grow relative p-0 min-h-[300px]">
        {/* 調試信息 */}
        <div className="absolute top-2 left-2 z-20 bg-black/70 text-white p-2 rounded text-xs">
          <div>Loading: {isLoading ? 'true' : 'false'}</div>
          <div>MapInstance: {mapInstance ? 'exists' : 'null'}</div>
          <div>Spots: {clientProcessedSpots.length}</div>
        </div>

        {isLoading && (
           <div className="absolute inset-0 flex items-center justify-center bg-muted/50 z-10">
            <Skeleton className="w-full h-full" />
            <p className="absolute text-foreground">地图加载中...</p>
          </div>
        )}
        <div
          ref={mapContainerRef}
          className="w-full h-full min-h-[300px] bg-gray-100 border-2 border-blue-200"
          style={{ visibility: isLoading ? 'hidden' : 'visible' }}
        />
         {!isLoading && !mapInstance && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center p-4">
              <p className="text-gray-600 mb-2">地图加载失败</p>
              <p className="text-sm text-gray-500">请检查网络连接或API密钥配置</p>
              <p className="text-xs text-gray-400 mt-2">調試: 檢查控制台日誌</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

    