
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CarFrontIcon, ClockIcon, DollarSignIcon, InfoIcon, MapPinIcon, WifiIcon, WavesIcon, VideoIcon } from "lucide-react";

export interface ParkingSpot {
  id: string;
  name: string;
  available: number;
  total: number;
  rate: string;
  hours: string;
  restrictions?: string;
  type: 'garage' | 'street' | 'lot';
  distance?: string;
  detectionMethod?: "地磁传感" | "超声波传感" | "视频监控";
  lat: number; // 新增纬度
  lng: number; // 新增经度
}

interface ParkingAvailabilityCardProps {
  spot: ParkingSpot;
}

export function ParkingAvailabilityCard({ spot }: ParkingAvailabilityCardProps) {
  const availabilityPercentage = spot.total > 0 ? (spot.available / spot.total) * 100 : 0;
  
  let availabilityVariant: "default" | "destructive" | "secondary" = "default";
  let availabilityText = `${spot.available} 空余`;
  let progressColorClass = "[&>div]:bg-primary";

  if (spot.available === 0) {
    availabilityVariant = "destructive";
    availabilityText = "已满";
    progressColorClass = "[&>div]:bg-destructive";
  } else if (availabilityPercentage < 25) {
    availabilityVariant = "secondary"; 
    progressColorClass = "[&>div]:bg-orange-500"; 
  }

  const typeTranslations = {
    garage: "室内车库",
    street: "路边",
    lot: "露天停车场"
  };

  const getDetectionIcon = () => {
    if (!spot.detectionMethod) return null;
    switch (spot.detectionMethod) {
      case "地磁传感":
        return <WifiIcon className="h-3.5 w-3.5 mr-1 text-muted-foreground transform rotate-90" title="地磁传感"/>;
      case "超声波传感":
        return <WavesIcon className="h-3.5 w-3.5 mr-1 text-muted-foreground" title="超声波传感"/>;
      case "视频监控":
        return <VideoIcon className="h-3.5 w-3.5 mr-1 text-muted-foreground" title="视频监控"/>;
      default:
        return null;
    }
  };

  const handleNavigation = () => {
    if (spot.lat && spot.lng) {
      const encodedName = encodeURIComponent(spot.name);
      // 使用高德地图URI scheme进行网页导航，优先尝试唤起App
      const amapNavUrl = `https://uri.amap.com/navigation?to=${spot.lng},${spot.lat},${encodedName}&mode=driving&coordinate=gaode&callnative=1&src=停车智导`;
      window.open(amapNavUrl, '_blank');
    } else {
      alert("该停车位坐标信息不完整，无法导航。");
    }
  };

  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{spot.name}</CardTitle>
          <Badge 
            variant={availabilityVariant} 
            className={availabilityVariant === 'default' ? 'bg-accent text-accent-foreground' : ''}
          >
            {availabilityText}
          </Badge>
        </div>
        <CardDescription className="capitalize flex items-center text-sm">
          <CarFrontIcon className="h-4 w-4 mr-1.5 text-muted-foreground" />
          {typeTranslations[spot.type] || spot.type} {spot.distance ? `• ${spot.distance}` : ''}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3 flex-grow">
        <div>
          <div className="flex justify-between text-xs text-muted-foreground mb-1">
            <span>可用车位数</span>
            <span>{spot.available} / {spot.total} 个车位</span>
          </div>
          <Progress value={availabilityPercentage} className={`h-2 ${progressColorClass}`} />
        </div>
        <div className="flex items-center text-sm text-foreground">
          <DollarSignIcon className="h-4 w-4 mr-2 text-primary" />
          <span>{spot.rate}</span>
        </div>
        <div className="flex items-center text-sm text-foreground">
          <ClockIcon className="h-4 w-4 mr-2 text-primary" />
          <span>{spot.hours}</span>
        </div>
         {spot.detectionMethod && (
          <div className="flex items-center text-xs text-muted-foreground pt-1">
            {getDetectionIcon()}
            <span>检测方式: {spot.detectionMethod}</span>
          </div>
        )}
        {spot.restrictions && (
          <div className="flex items-start text-xs text-muted-foreground pt-1">
            <InfoIcon className="h-3.5 w-3.5 mr-1.5 mt-0.5 shrink-0 text-amber-600" />
            <span>{spot.restrictions}</span>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-3">
        <Button variant="outline" className="w-full" onClick={handleNavigation}>
          <MapPinIcon className="mr-2 h-4 w-4" />
          地图导航
        </Button>
      </CardFooter>
    </Card>
  );
}
